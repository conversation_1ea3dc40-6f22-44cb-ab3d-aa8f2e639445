#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
海盐光伏聚合系统自动登录脚本
"""

import time
import logging
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from config import LOGIN_CONFIG, BROWSER_CONFIG, LOG_CONFIG, RETRY_CONFIG

# 配置日志
logging.basicConfig(
    level=getattr(logging, LOG_CONFIG['level']),
    format=LOG_CONFIG['format'],
    handlers=[
        logging.FileHandler(LOG_CONFIG['file'], encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HaiYanAutoLogin:
    def __init__(self):
        self.url = LOGIN_CONFIG['url']
        self.username = LOGIN_CONFIG['username']
        self.password = LOGIN_CONFIG['password']
        self.driver = None
        self.wait = None
        self.timeout = BROWSER_CONFIG['timeout']
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        try:
            chrome_options = Options()
            # 可选：无头模式（后台运行）
            # chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option('useAutomationExtension', False)
            
            # 初始化驱动（自动管理ChromeDriver）
            service = Service(ChromeDriverManager().install())
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            self.wait = WebDriverWait(self.driver, 10)
            
            logger.info("浏览器驱动初始化成功")
            return True
            
        except Exception as e:
            logger.error(f"浏览器驱动初始化失败: {e}")
            return False
    
    def login(self):
        """执行登录流程"""
        try:
            # 打开登录页面
            logger.info(f"正在访问登录页面: {self.url}")
            self.driver.get(self.url)
            time.sleep(3)
            
            # 等待页面加载完成
            self.wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            
            # 查找并填写用户名（多种定位方式）
            username_input = None
            username_selectors = [
                (By.NAME, "username"),
                (By.ID, "username"),
                (By.XPATH, "//input[@placeholder='用户名' or @placeholder='账号' or contains(@class, 'username')]"),
                (By.CSS_SELECTOR, "input[type='text']:first-of-type")
            ]

            for selector in username_selectors:
                try:
                    username_input = self.wait.until(EC.presence_of_element_located(selector))
                    break
                except TimeoutException:
                    continue

            if username_input:
                username_input.clear()
                username_input.send_keys(self.username)
                logger.info("用户名填写完成")
            else:
                raise Exception("无法找到用户名输入框")

            # 查找并填写密码（多种定位方式）
            password_input = None
            password_selectors = [
                (By.NAME, "password"),
                (By.ID, "password"),
                (By.XPATH, "//input[@type='password' or @placeholder='密码']"),
                (By.CSS_SELECTOR, "input[type='password']")
            ]

            for selector in password_selectors:
                try:
                    password_input = self.driver.find_element(*selector)
                    break
                except NoSuchElementException:
                    continue

            if password_input:
                password_input.clear()
                password_input.send_keys(self.password)
                logger.info("密码填写完成")
            else:
                raise Exception("无法找到密码输入框")
            
            # 处理验证码
            self.handle_captcha()
            
            # 点击登录按钮（多种定位方式）
            login_button = None
            login_selectors = [
                (By.XPATH, "//button[contains(text(), '登录')]"),
                (By.XPATH, "//input[@type='submit' and @value='登录']"),
                (By.XPATH, "//button[contains(@class, 'login')]"),
                (By.CSS_SELECTOR, "button[type='submit']"),
                (By.XPATH, "//a[contains(text(), '登录')]")
            ]

            for selector in login_selectors:
                try:
                    login_button = self.driver.find_element(*selector)
                    break
                except NoSuchElementException:
                    continue

            if login_button:
                login_button.click()
                logger.info("点击登录按钮")
            else:
                raise Exception("无法找到登录按钮")
            
            # 等待登录结果
            time.sleep(3)
            
            # 处理登录后的验证
            self.handle_post_login_verification()
            
            return True
            
        except TimeoutException:
            logger.error("页面元素加载超时")
            return False
        except NoSuchElementException as e:
            logger.error(f"找不到页面元素: {e}")
            return False
        except Exception as e:
            logger.error(f"登录过程中发生错误: {e}")
            return False
    
    def handle_captcha(self):
        """处理验证码"""
        try:
            # 查找验证码输入框（多种定位方式）
            captcha_input = None
            captcha_selectors = [
                (By.NAME, "captcha"),
                (By.NAME, "verifyCode"),
                (By.ID, "captcha"),
                (By.ID, "verifyCode"),
                (By.XPATH, "//input[@placeholder='验证码' or @placeholder='图形验证码']"),
                (By.CSS_SELECTOR, "input[maxlength='4'], input[maxlength='5'], input[maxlength='6']")
            ]

            for selector in captcha_selectors:
                try:
                    captcha_input = self.driver.find_element(*selector)
                    break
                except NoSuchElementException:
                    continue

            if captcha_input:
                # 提示用户输入验证码
                print("\n" + "="*50)
                print("请查看浏览器窗口中的验证码图片")
                print("验证码通常在登录框附近显示")
                captcha_code = input("请输入验证码（如果没有验证码请直接按回车）: ").strip()
                print("="*50 + "\n")

                if captcha_code:
                    captcha_input.clear()
                    captcha_input.send_keys(captcha_code)
                    logger.info("验证码输入完成")
                else:
                    logger.info("用户跳过验证码输入")
            else:
                logger.info("未找到验证码输入框，可能不需要验证码")

        except Exception as e:
            logger.error(f"处理验证码时发生错误: {e}")
    
    def handle_post_login_verification(self):
        """处理登录后的验证（短信验证）"""
        try:
            # 等待验证弹窗出现
            time.sleep(2)
            
            # 查找短信验证选项
            sms_verification = self.wait.until(
                EC.element_to_be_clickable((By.XPATH, "//button[contains(text(), '短信验证') or contains(text(), '短信')]"))
            )
            sms_verification.click()
            logger.info("选择短信验证")
            
            # 等待短信验证码输入框
            sms_input = self.wait.until(
                EC.presence_of_element_located((By.NAME, "smsCode"))
            )
            
            # 提示用户输入短信验证码
            print("\n" + "="*50)
            print("短信验证码已发送，请查看手机短信")
            sms_code = input("请输入短信验证码: ").strip()
            print("="*50 + "\n")
            
            sms_input.clear()
            sms_input.send_keys(sms_code)
            
            # 点击确认按钮
            confirm_button = self.driver.find_element(By.XPATH, "//button[contains(text(), '确认') or contains(text(), '提交')]")
            confirm_button.click()
            logger.info("短信验证码提交完成")
            
            # 等待验证完成
            time.sleep(3)
            logger.info("登录验证流程完成")
            
        except TimeoutException:
            logger.warning("未找到验证弹窗，可能已经登录成功")
        except Exception as e:
            logger.error(f"处理登录后验证时发生错误: {e}")
    
    def check_login_status(self):
        """检查登录状态"""
        try:
            # 检查是否成功进入系统
            current_url = self.driver.current_url
            if "login" not in current_url.lower():
                logger.info("登录成功！")
                return True
            else:
                logger.warning("可能登录失败，仍在登录页面")
                return False
                
        except Exception as e:
            logger.error(f"检查登录状态时发生错误: {e}")
            return False
    
    def run(self):
        """运行自动登录流程"""
        try:
            logger.info("开始自动登录流程")
            
            # 设置浏览器驱动
            if not self.setup_driver():
                return False
            
            # 执行登录
            if self.login():
                # 检查登录状态
                if self.check_login_status():
                    logger.info("自动登录流程完成！浏览器将保持打开状态")
                    input("按回车键关闭浏览器...")
                else:
                    logger.warning("登录可能未成功，请手动检查")
            
            return True
            
        except Exception as e:
            logger.error(f"自动登录流程发生错误: {e}")
            return False
        finally:
            # 清理资源
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")

def main():
    """主函数"""
    print("海盐光伏聚合系统自动登录脚本")
    print("="*50)
    
    auto_login = HaiYanAutoLogin()
    success = auto_login.run()
    
    if success:
        print("脚本执行完成")
    else:
        print("脚本执行失败，请查看日志文件")

if __name__ == "__main__":
    main()
