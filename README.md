# 海盐光伏聚合系统自动登录脚本

这个脚本可以自动化登录海盐光伏聚合系统，处理验证码和短信验证流程。

## 功能特点

- 自动填写用户名和密码
- 交互式验证码输入
- 自动选择短信验证
- 交互式短信验证码输入
- 详细的日志记录
- 错误处理和重试机制

## 安装要求

### 1. Python环境
确保您的系统已安装Python 3.7或更高版本。

### 2. Chrome浏览器
需要安装Google Chrome浏览器。

### 3. ChromeDriver
脚本会自动管理ChromeDriver，但如果遇到问题，可以手动下载：
- 访问 https://chromedriver.chromium.org/
- 下载与您的Chrome版本匹配的ChromeDriver
- 将ChromeDriver添加到系统PATH中

## 安装步骤

### 1. 克隆或下载脚本文件
将以下文件保存到同一个目录：
- `auto_login.py` - 主脚本文件
- `requirements.txt` - 依赖包列表
- `README.md` - 说明文档

### 2. 安装Python依赖
打开终端/命令提示符，导航到脚本目录，运行：

```bash
pip install -r requirements.txt
```

或者单独安装：
```bash
pip install selenium webdriver-manager
```

## 使用方法

### 1. 运行脚本
在终端中运行：
```bash
python auto_login.py
```

### 2. 交互流程
脚本运行后会：
1. 自动打开Chrome浏览器
2. 导航到登录页面
3. 自动填写用户名和密码
4. 提示您输入验证码（如果有）
5. 点击登录
6. 自动选择短信验证
7. 提示您输入短信验证码
8. 完成登录流程

### 3. 输入验证码
当脚本提示输入验证码时：
- 查看浏览器窗口中显示的验证码图片
- 在终端中输入验证码
- 按回车确认

### 4. 输入短信验证码
当脚本提示输入短信验证码时：
- 查看手机收到的短信验证码
- 在终端中输入验证码
- 按回车确认

## 配置说明

如需修改登录信息，请编辑 `auto_login.py` 文件中的以下变量：

```python
self.url = "https://zjpx.com.cn/zjpx/login#/outNet"  # 登录网址
self.username = "HYHCDF1"                            # 用户名
self.password = "Nottingham15721?"                   # 密码
```

## 日志文件

脚本会生成 `login.log` 日志文件，记录详细的执行过程和错误信息。

## 故障排除

### 1. ChromeDriver问题
如果遇到ChromeDriver相关错误：
- 确保Chrome浏览器已安装并是最新版本
- 尝试手动下载匹配的ChromeDriver版本

### 2. 元素定位问题
如果脚本无法找到页面元素：
- 网站可能更新了页面结构
- 需要更新脚本中的元素定位器

### 3. 网络问题
- 确保网络连接正常
- 检查是否需要代理设置

### 4. 验证码识别
- 目前需要手动输入验证码
- 如需自动识别，可以集成OCR服务

## 安全注意事项

- 请妥善保管登录凭据
- 建议在安全的网络环境中使用
- 定期更新密码
- 不要在公共计算机上保存脚本

## 免责声明

此脚本仅供学习和个人使用。使用者需要：
- 遵守相关网站的使用条款
- 承担使用风险
- 确保合法合规使用

## 技术支持

如遇到问题，请检查：
1. Python版本是否兼容
2. 依赖包是否正确安装
3. Chrome浏览器是否正常工作
4. 网络连接是否稳定
5. 日志文件中的错误信息
