#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本 - 验证环境配置
"""

import sys
import os

def test_imports():
    """测试依赖包导入"""
    print("🔍 测试依赖包导入...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from webdriver_manager.chrome import ChromeDriverManager
        print("✅ 所有依赖包导入成功")
        return True
    except ImportError as e:
        print(f"❌ 依赖包导入失败: {e}")
        return False

def test_chrome():
    """测试Chrome浏览器"""
    print("🔍 测试Chrome浏览器...")
    chrome_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    if os.path.exists(chrome_path):
        print("✅ Chrome浏览器已安装")
        return True
    else:
        print("❌ Chrome浏览器未找到")
        return False

def test_webdriver():
    """测试WebDriver"""
    print("🔍 测试WebDriver...")
    try:
        from selenium import webdriver
        from selenium.webdriver.chrome.options import Options
        from selenium.webdriver.chrome.service import Service
        from webdriver_manager.chrome import ChromeDriverManager
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式测试
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.binary_location = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        
        # 创建WebDriver
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 简单测试
        driver.get("https://www.baidu.com")
        title = driver.title
        driver.quit()
        
        print(f"✅ WebDriver测试成功，访问页面标题: {title}")
        return True
        
    except Exception as e:
        print(f"❌ WebDriver测试失败: {e}")
        return False

def test_config():
    """测试配置文件"""
    print("🔍 测试配置文件...")
    try:
        from config import LOGIN_CONFIG, BROWSER_CONFIG
        print(f"✅ 配置文件加载成功")
        print(f"   登录URL: {LOGIN_CONFIG['url']}")
        print(f"   用户名: {LOGIN_CONFIG['username']}")
        return True
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("海盐光伏聚合系统自动登录 - 环境测试")
    print("=" * 50)
    
    tests = [
        ("依赖包导入", test_imports),
        ("Chrome浏览器", test_chrome),
        ("配置文件", test_config),
        ("WebDriver", test_webdriver),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！环境配置正确，可以运行自动登录脚本")
        return True
    else:
        print("⚠️  部分测试失败，请检查环境配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
