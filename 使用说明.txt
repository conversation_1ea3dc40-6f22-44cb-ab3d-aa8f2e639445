海盐光伏聚合系统自动登录脚本使用说明
==========================================

快速开始：
1. 确保已安装Python 3.7+和Chrome浏览器
2. 双击运行"启动脚本.bat"（Windows）或在终端运行"python run.py"
3. 按提示输入验证码和短信验证码

文件说明：
- auto_login.py：主脚本文件
- config.py：配置文件（可修改登录信息）
- run.py：简化启动脚本
- 启动脚本.bat：Windows批处理文件
- requirements.txt：依赖包列表
- README.md：详细说明文档

修改登录信息：
编辑config.py文件中的LOGIN_CONFIG部分：
- url：登录网址
- username：用户名
- password：密码

常见问题：
1. 如果提示找不到元素，可能是网页结构发生变化
2. 如果Chrome版本不匹配，脚本会自动下载对应版本的驱动
3. 验证码需要手动输入，短信验证码也需要手动输入
4. 脚本执行过程中请不要关闭浏览器窗口

注意事项：
- 请确保网络连接稳定
- 手机能正常接收短信验证码
- 首次运行可能需要下载ChromeDriver，请耐心等待

技术支持：
如遇问题请查看login.log日志文件获取详细错误信息
