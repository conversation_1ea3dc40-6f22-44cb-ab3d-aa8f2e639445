#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的启动脚本
"""

import sys
import subprocess
import os

def check_requirements():
    """检查并安装依赖"""
    try:
        import selenium
        from webdriver_manager.chrome import ChromeDriverManager
        print("✓ 依赖包已安装")
        return True
    except ImportError:
        print("正在安装依赖包...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            print("✓ 依赖包安装完成")
            return True
        except subprocess.CalledProcessError:
            print("✗ 依赖包安装失败")
            return False

def main():
    print("海盐光伏聚合系统自动登录")
    print("=" * 40)
    
    # 检查依赖
    if not check_requirements():
        print("请手动安装依赖: pip install -r requirements.txt")
        return
    
    # 运行主脚本
    try:
        from auto_login import main as auto_login_main
        auto_login_main()
    except Exception as e:
        print(f"运行出错: {e}")
        print("请检查 login.log 文件获取详细错误信息")

if __name__ == "__main__":
    main()
