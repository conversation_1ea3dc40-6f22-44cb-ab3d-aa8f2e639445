#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置文件
"""

# 登录配置
LOGIN_CONFIG = {
    "url": "https://zjpx.com.cn/zjpx/login#/outNet",
    "username": "HYHCDF1",
    "password": "Nottingham15721?"
}

# 浏览器配置
BROWSER_CONFIG = {
    "headless": False,  # 是否无头模式（后台运行）
    "window_size": "1920,1080",  # 窗口大小
    "timeout": 10,  # 元素等待超时时间（秒）
    "implicit_wait": 3  # 隐式等待时间（秒）
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",  # 日志级别：DEBUG, INFO, WARNING, ERROR
    "file": "login.log",  # 日志文件名
    "format": "%(asctime)s - %(levelname)s - %(message)s"
}

# 重试配置
RETRY_CONFIG = {
    "max_retries": 3,  # 最大重试次数
    "retry_delay": 2  # 重试间隔（秒）
}
